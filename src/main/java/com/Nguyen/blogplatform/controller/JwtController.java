package com.Nguyen.blogplatform.controller;

import com.Nguyen.blogplatform.service.JwtTokenService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/api/v1/jwt")
@RequiredArgsConstructor
@Tag(name = "JWT", description = "JWT token utilities")
public class JwtController {
    
    private final JwtTokenService jwtTokenService;
    
    @PostMapping("/decode")
    @Operation(summary = "Decode JWT token", description = "Decode JWT token and extract user information and roles")
    public ResponseEntity<Map<String, Object>> decodeToken(
            @Parameter(description = "JWT token to decode") @RequestBody Map<String, String> request) {
        
        String token = request.get("token");
        if (token == null || token.isEmpty()) {
            return ResponseEntity.badRequest().body(Map.of("error", "Token is required"));
        }
        
        // Remove "Bearer " prefix if present
        if (token.startsWith("Bearer ")) {
            token = token.substring(7);
        }
        
        if (!jwtTokenService.isTokenValid(token)) {
            return ResponseEntity.badRequest().body(Map.of("error", "Invalid token"));
        }
        
        Map<String, Object> response = new HashMap<>();
        response.put("userId", jwtTokenService.extractUserId(token));
        response.put("username", jwtTokenService.extractUsername(token));
        response.put("roles", jwtTokenService.extractRoles(token));
        response.put("isAdmin", jwtTokenService.isAdmin(token));
        response.put("isAuthor", jwtTokenService.isAuthor(token));
        response.put("isUser", jwtTokenService.isUser(token));
        response.put("tokenInfo", jwtTokenService.getTokenInfo(token));
        
        return ResponseEntity.ok(response);
    }
    
    @GetMapping("/roles")
    @Operation(summary = "Get user roles from token", description = "Extract roles from JWT token in Authorization header")
    public ResponseEntity<Map<String, Object>> getUserRoles(
            @Parameter(description = "JWT token in Authorization header") 
            @RequestHeader(value = "Authorization", required = false) String authHeader) {
        
        if (authHeader == null || !authHeader.startsWith("Bearer ")) {
            return ResponseEntity.badRequest().body(Map.of("error", "Authorization header with Bearer token is required"));
        }
        
        String token = authHeader.substring(7);
        
        if (!jwtTokenService.isTokenValid(token)) {
            return ResponseEntity.badRequest().body(Map.of("error", "Invalid token"));
        }
        
        List<String> roles = jwtTokenService.extractRoles(token);
        String username = jwtTokenService.extractUsername(token);
        
        Map<String, Object> response = new HashMap<>();
        response.put("username", username);
        response.put("roles", roles);
        response.put("hasAdminRole", jwtTokenService.isAdmin(token));
        response.put("hasAuthorRole", jwtTokenService.isAuthor(token));
        response.put("hasUserRole", jwtTokenService.isUser(token));
        
        return ResponseEntity.ok(response);
    }
    
    @GetMapping("/validate")
    @Operation(summary = "Validate JWT token", description = "Check if JWT token is valid")
    public ResponseEntity<Map<String, Object>> validateToken(
            @Parameter(description = "JWT token in Authorization header") 
            @RequestHeader(value = "Authorization", required = false) String authHeader) {
        
        if (authHeader == null || !authHeader.startsWith("Bearer ")) {
            return ResponseEntity.badRequest().body(Map.of("error", "Authorization header with Bearer token is required"));
        }
        
        String token = authHeader.substring(7);
        boolean isValid = jwtTokenService.isTokenValid(token);
        
        Map<String, Object> response = new HashMap<>();
        response.put("valid", isValid);
        
        if (isValid) {
            response.put("userId", jwtTokenService.extractUserId(token));
            response.put("username", jwtTokenService.extractUsername(token));
            response.put("roles", jwtTokenService.extractRoles(token));
        }
        
        return ResponseEntity.ok(response);
    }
}
