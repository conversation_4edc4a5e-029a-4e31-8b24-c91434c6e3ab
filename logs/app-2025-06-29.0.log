2025-06-29 00:00:54 [http-nio-8888-exec-3] DEBUG o.s.security.web.FilterChainProxy - Securing GET /api/v1/auth/me
2025-06-29 00:00:54 [http-nio-8888-exec-3] DEBUG o.s.security.web.FilterChainProxy - Secured GET /api/v1/auth/me
2025-06-29 00:00:59 [http-nio-8888-exec-4] DEBUG o.s.security.web.FilterChainProxy - Securing GET /api/v1/auth/me
2025-06-29 00:00:59 [http-nio-8888-exec-4] DEBUG o.s.security.web.FilterChainProxy - Secured GET /api/v1/auth/me
2025-06-29 00:01:01 [http-nio-8888-exec-5] DEBUG o.s.security.web.FilterChainProxy - Securing GET /api/v1/auth/me
2025-06-29 00:01:01 [http-nio-8888-exec-7] DEBUG o.s.security.web.FilterChainProxy - Securing GET /api/v1/auth/me
2025-06-29 00:01:01 [http-nio-8888-exec-8] DEBUG o.s.security.web.FilterChainProxy - Securing GET /api/v1/auth/me
2025-06-29 00:01:01 [http-nio-8888-exec-6] DEBUG o.s.security.web.FilterChainProxy - Securing GET /api/v1/auth/me
2025-06-29 00:01:01 [http-nio-8888-exec-5] DEBUG o.s.security.web.FilterChainProxy - Secured GET /api/v1/auth/me
2025-06-29 00:01:02 [http-nio-8888-exec-9] DEBUG o.s.security.web.FilterChainProxy - Securing GET /api/v1/auth/me
2025-06-29 00:01:02 [http-nio-8888-exec-10] DEBUG o.s.security.web.FilterChainProxy - Securing GET /api/v1/auth/me
2025-06-29 00:01:02 [http-nio-8888-exec-10] DEBUG o.s.security.web.FilterChainProxy - Secured GET /api/v1/auth/me
2025-06-29 00:01:02 [http-nio-8888-exec-9] DEBUG o.s.security.web.FilterChainProxy - Secured GET /api/v1/auth/me
2025-06-29 00:01:06 [http-nio-8888-exec-6] DEBUG o.s.security.web.FilterChainProxy - Secured GET /api/v1/auth/me
2025-06-29 00:01:08 [http-nio-8888-exec-7] DEBUG o.s.security.web.FilterChainProxy - Secured GET /api/v1/auth/me
2025-06-29 00:01:08 [http-nio-8888-exec-8] DEBUG o.s.security.web.FilterChainProxy - Secured GET /api/v1/auth/me
2025-06-29 00:01:23 [http-nio-8888-exec-1] DEBUG o.s.security.web.FilterChainProxy - Securing GET /api/v1/auth/me
2025-06-29 00:01:23 [http-nio-8888-exec-1] DEBUG o.s.security.web.FilterChainProxy - Secured GET /api/v1/auth/me
2025-06-29 00:01:27 [http-nio-8888-exec-2] DEBUG o.s.security.web.FilterChainProxy - Securing GET /api/v1/auth/me
2025-06-29 00:01:27 [http-nio-8888-exec-2] DEBUG o.s.security.web.FilterChainProxy - Secured GET /api/v1/auth/me
2025-06-29 00:02:06 [http-nio-8888-exec-5] DEBUG o.s.security.web.FilterChainProxy - Securing GET /api/v1/auth/me
2025-06-29 00:02:06 [http-nio-8888-exec-5] DEBUG o.s.security.web.FilterChainProxy - Secured GET /api/v1/auth/me
2025-06-29 00:02:08 [http-nio-8888-exec-6] DEBUG o.s.security.web.FilterChainProxy - Securing GET /api/v1/auth/me
2025-06-29 00:02:08 [http-nio-8888-exec-6] DEBUG o.s.security.web.FilterChainProxy - Secured GET /api/v1/auth/me
2025-06-29 00:04:35 [http-nio-8888-exec-8] DEBUG o.s.security.web.FilterChainProxy - Securing GET /api/v1/auth/me
2025-06-29 00:04:35 [http-nio-8888-exec-8] DEBUG o.s.security.web.FilterChainProxy - Secured GET /api/v1/auth/me
2025-06-29 00:04:38 [http-nio-8888-exec-1] DEBUG o.s.security.web.FilterChainProxy - Securing GET /api/v1/auth/me
2025-06-29 00:04:39 [http-nio-8888-exec-1] DEBUG o.s.security.web.FilterChainProxy - Secured GET /api/v1/auth/me
2025-06-29 00:04:41 [http-nio-8888-exec-2] DEBUG o.s.security.web.FilterChainProxy - Securing GET /api/v1/auth/me
2025-06-29 00:04:41 [http-nio-8888-exec-2] DEBUG o.s.security.web.FilterChainProxy - Secured GET /api/v1/auth/me
2025-06-29 00:12:55 [MessageBroker-2] INFO  o.s.w.s.c.WebSocketMessageBrokerStats - WebSocketSession[0 current WS(0)-HttpStream(0)-HttpPoll(0), 0 total, 0 closed abnormally (0 connect failure, 0 send limit, 0 transport error)], stompSubProtocol[processed CONNECT(0)-CONNECTED(0)-DISCONNECT(0)], stompBrokerRelay[null], inboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], outboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], sockJsScheduler[pool size = 3, active threads = 1, queued tasks = 0, completed tasks = 2]
2025-06-29 00:42:55 [MessageBroker-1] INFO  o.s.w.s.c.WebSocketMessageBrokerStats - WebSocketSession[0 current WS(0)-HttpStream(0)-HttpPoll(0), 0 total, 0 closed abnormally (0 connect failure, 0 send limit, 0 transport error)], stompSubProtocol[processed CONNECT(0)-CONNECTED(0)-DISCONNECT(0)], stompBrokerRelay[null], inboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], outboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], sockJsScheduler[pool size = 4, active threads = 1, queued tasks = 0, completed tasks = 3]
2025-06-29 00:52:08 [http-nio-8888-exec-4] DEBUG o.s.security.web.FilterChainProxy - Securing GET /api/v1/auth/me
2025-06-29 00:52:08 [http-nio-8888-exec-4] DEBUG o.s.security.web.FilterChainProxy - Secured GET /api/v1/auth/me
2025-06-29 01:01:10 [http-nio-8888-exec-9] DEBUG o.s.security.web.FilterChainProxy - Securing GET /api/v1/auth/me
2025-06-29 01:01:11 [http-nio-8888-exec-9] DEBUG o.s.security.web.FilterChainProxy - Secured GET /api/v1/auth/me
2025-06-29 01:01:21 [http-nio-8888-exec-10] DEBUG o.s.security.web.FilterChainProxy - Securing GET /api/v1/auth/me
2025-06-29 01:01:21 [http-nio-8888-exec-10] DEBUG o.s.security.web.FilterChainProxy - Secured GET /api/v1/auth/me
2025-06-29 01:01:43 [http-nio-8888-exec-6] DEBUG o.s.security.web.FilterChainProxy - Securing GET /api/v1/auth/me
2025-06-29 01:01:43 [http-nio-8888-exec-6] DEBUG o.s.security.web.FilterChainProxy - Secured GET /api/v1/auth/me
2025-06-29 01:01:46 [http-nio-8888-exec-7] DEBUG o.s.security.web.FilterChainProxy - Securing GET /api/v1/auth/me
2025-06-29 01:01:46 [http-nio-8888-exec-7] DEBUG o.s.security.web.FilterChainProxy - Secured GET /api/v1/auth/me
2025-06-29 01:02:57 [http-nio-8888-exec-1] DEBUG o.s.security.web.FilterChainProxy - Securing GET /api/v1/auth/me
2025-06-29 01:02:57 [http-nio-8888-exec-1] DEBUG o.s.security.web.FilterChainProxy - Secured GET /api/v1/auth/me
2025-06-29 01:02:58 [http-nio-8888-exec-2] DEBUG o.s.security.web.FilterChainProxy - Securing GET /api/v1/auth/me
2025-06-29 01:02:58 [http-nio-8888-exec-2] DEBUG o.s.security.web.FilterChainProxy - Secured GET /api/v1/auth/me
2025-06-29 01:03:01 [http-nio-8888-exec-3] DEBUG o.s.security.web.FilterChainProxy - Securing GET /api/v1/auth/me
2025-06-29 01:03:01 [http-nio-8888-exec-3] DEBUG o.s.security.web.FilterChainProxy - Secured GET /api/v1/auth/me
2025-06-29 01:03:16 [http-nio-8888-exec-4] DEBUG o.s.security.web.FilterChainProxy - Securing GET /api/v1/auth/me
2025-06-29 01:03:16 [http-nio-8888-exec-4] DEBUG o.s.security.web.FilterChainProxy - Secured GET /api/v1/auth/me
2025-06-29 01:03:48 [http-nio-8888-exec-5] DEBUG o.s.security.web.FilterChainProxy - Securing GET /api/v1/auth/me
2025-06-29 01:03:48 [http-nio-8888-exec-5] DEBUG o.s.security.web.FilterChainProxy - Secured GET /api/v1/auth/me
2025-06-29 01:06:32 [http-nio-8888-exec-10] DEBUG o.s.security.web.FilterChainProxy - Securing GET /api/v1/auth/me
2025-06-29 01:06:32 [http-nio-8888-exec-10] DEBUG o.s.security.web.FilterChainProxy - Secured GET /api/v1/auth/me
2025-06-29 01:06:33 [http-nio-8888-exec-6] DEBUG o.s.security.web.FilterChainProxy - Securing GET /api/v1/auth/me
2025-06-29 01:06:33 [http-nio-8888-exec-6] DEBUG o.s.security.web.FilterChainProxy - Secured GET /api/v1/auth/me
2025-06-29 01:06:36 [http-nio-8888-exec-7] DEBUG o.s.security.web.FilterChainProxy - Securing GET /api/v1/auth/me
2025-06-29 01:06:36 [http-nio-8888-exec-7] DEBUG o.s.security.web.FilterChainProxy - Secured GET /api/v1/auth/me
2025-06-29 01:06:39 [http-nio-8888-exec-8] DEBUG o.s.security.web.FilterChainProxy - Securing GET /api/v1/auth/me
2025-06-29 01:06:39 [http-nio-8888-exec-8] DEBUG o.s.security.web.FilterChainProxy - Secured GET /api/v1/auth/me
2025-06-29 01:06:40 [http-nio-8888-exec-1] DEBUG o.s.security.web.FilterChainProxy - Securing GET /api/v1/auth/me
2025-06-29 01:06:40 [http-nio-8888-exec-1] DEBUG o.s.security.web.FilterChainProxy - Secured GET /api/v1/auth/me
2025-06-29 01:06:51 [http-nio-8888-exec-2] DEBUG o.s.security.web.FilterChainProxy - Securing GET /api/v1/auth/me
2025-06-29 01:06:51 [http-nio-8888-exec-2] DEBUG o.s.security.web.FilterChainProxy - Secured GET /api/v1/auth/me
2025-06-29 01:06:55 [http-nio-8888-exec-3] DEBUG o.s.security.web.FilterChainProxy - Securing GET /api/v1/auth/me
2025-06-29 01:06:55 [http-nio-8888-exec-3] DEBUG o.s.security.web.FilterChainProxy - Secured GET /api/v1/auth/me
2025-06-29 01:07:17 [http-nio-8888-exec-4] DEBUG o.s.security.web.FilterChainProxy - Securing GET /api/v1/auth/me
2025-06-29 01:07:17 [http-nio-8888-exec-4] DEBUG o.s.security.web.FilterChainProxy - Secured GET /api/v1/auth/me
2025-06-29 01:08:24 [http-nio-8888-exec-9] DEBUG o.s.security.web.FilterChainProxy - Securing GET /api/v1/auth/me
2025-06-29 01:08:25 [http-nio-8888-exec-9] DEBUG o.s.security.web.FilterChainProxy - Secured GET /api/v1/auth/me
2025-06-29 01:08:28 [http-nio-8888-exec-10] DEBUG o.s.security.web.FilterChainProxy - Securing GET /api/v1/auth/me
2025-06-29 01:08:28 [http-nio-8888-exec-10] DEBUG o.s.security.web.FilterChainProxy - Secured GET /api/v1/auth/me
2025-06-29 01:08:30 [http-nio-8888-exec-6] DEBUG o.s.security.web.FilterChainProxy - Securing GET /api/v1/auth/me
2025-06-29 01:08:30 [http-nio-8888-exec-6] DEBUG o.s.security.web.FilterChainProxy - Secured GET /api/v1/auth/me
2025-06-29 01:08:32 [http-nio-8888-exec-7] DEBUG o.s.security.web.FilterChainProxy - Securing GET /api/v1/auth/me
2025-06-29 01:08:32 [http-nio-8888-exec-7] DEBUG o.s.security.web.FilterChainProxy - Secured GET /api/v1/auth/me
2025-06-29 01:08:47 [http-nio-8888-exec-8] DEBUG o.s.security.web.FilterChainProxy - Securing GET /api/v1/auth/me
2025-06-29 01:08:47 [http-nio-8888-exec-8] DEBUG o.s.security.web.FilterChainProxy - Secured GET /api/v1/auth/me
2025-06-29 01:08:48 [http-nio-8888-exec-1] DEBUG o.s.security.web.FilterChainProxy - Securing GET /api/v1/auth/me
2025-06-29 01:08:48 [http-nio-8888-exec-1] DEBUG o.s.security.web.FilterChainProxy - Secured GET /api/v1/auth/me
2025-06-29 01:09:18 [http-nio-8888-exec-2] DEBUG o.s.security.web.FilterChainProxy - Securing GET /api/v1/auth/me
2025-06-29 01:09:18 [http-nio-8888-exec-2] DEBUG o.s.security.web.FilterChainProxy - Secured GET /api/v1/auth/me
2025-06-29 01:09:19 [http-nio-8888-exec-3] DEBUG o.s.security.web.FilterChainProxy - Securing GET /api/v1/auth/me
2025-06-29 01:09:19 [http-nio-8888-exec-3] DEBUG o.s.security.web.FilterChainProxy - Secured GET /api/v1/auth/me
2025-06-29 01:10:28 [http-nio-8888-exec-5] DEBUG o.s.security.web.FilterChainProxy - Securing GET /api/v1/auth/me
2025-06-29 01:10:28 [http-nio-8888-exec-5] DEBUG o.s.security.web.FilterChainProxy - Secured GET /api/v1/auth/me
2025-06-29 01:10:59 [http-nio-8888-exec-9] DEBUG o.s.security.web.FilterChainProxy - Securing GET /api/v1/auth/me
2025-06-29 01:11:00 [http-nio-8888-exec-9] DEBUG o.s.security.web.FilterChainProxy - Secured GET /api/v1/auth/me
2025-06-29 01:11:03 [http-nio-8888-exec-10] DEBUG o.s.security.web.FilterChainProxy - Securing GET /api/v1/auth/me
2025-06-29 01:11:03 [http-nio-8888-exec-10] DEBUG o.s.security.web.FilterChainProxy - Secured GET /api/v1/auth/me
2025-06-29 01:12:53 [http-nio-8888-exec-7] DEBUG o.s.security.web.FilterChainProxy - Securing GET /api/v1/auth/me
2025-06-29 01:12:53 [http-nio-8888-exec-7] DEBUG o.s.security.web.FilterChainProxy - Secured GET /api/v1/auth/me
2025-06-29 01:12:55 [MessageBroker-1] INFO  o.s.w.s.c.WebSocketMessageBrokerStats - WebSocketSession[0 current WS(0)-HttpStream(0)-HttpPoll(0), 0 total, 0 closed abnormally (0 connect failure, 0 send limit, 0 transport error)], stompSubProtocol[processed CONNECT(0)-CONNECTED(0)-DISCONNECT(0)], stompBrokerRelay[null], inboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], outboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], sockJsScheduler[pool size = 5, active threads = 1, queued tasks = 0, completed tasks = 4]
2025-06-29 01:13:09 [http-nio-8888-exec-8] DEBUG o.s.security.web.FilterChainProxy - Securing GET /api/v1/auth/me
2025-06-29 01:13:10 [http-nio-8888-exec-8] DEBUG o.s.security.web.FilterChainProxy - Secured GET /api/v1/auth/me
2025-06-29 01:13:11 [http-nio-8888-exec-1] DEBUG o.s.security.web.FilterChainProxy - Securing GET /api/v1/auth/me
2025-06-29 01:13:11 [http-nio-8888-exec-1] DEBUG o.s.security.web.FilterChainProxy - Secured GET /api/v1/auth/me
2025-06-29 01:13:14 [http-nio-8888-exec-2] DEBUG o.s.security.web.FilterChainProxy - Securing GET /api/v1/auth/me
2025-06-29 01:13:15 [http-nio-8888-exec-2] DEBUG o.s.security.web.FilterChainProxy - Secured GET /api/v1/auth/me
2025-06-29 01:13:16 [http-nio-8888-exec-3] DEBUG o.s.security.web.FilterChainProxy - Securing GET /api/v1/auth/me
2025-06-29 01:13:16 [http-nio-8888-exec-3] DEBUG o.s.security.web.FilterChainProxy - Secured GET /api/v1/auth/me
2025-06-29 01:13:19 [http-nio-8888-exec-4] DEBUG o.s.security.web.FilterChainProxy - Securing GET /api/v1/auth/me
2025-06-29 01:13:19 [http-nio-8888-exec-4] DEBUG o.s.security.web.FilterChainProxy - Secured GET /api/v1/auth/me
2025-06-29 01:13:37 [http-nio-8888-exec-5] DEBUG o.s.security.web.FilterChainProxy - Securing GET /api/v1/auth/me
2025-06-29 01:13:37 [http-nio-8888-exec-5] DEBUG o.s.security.web.FilterChainProxy - Secured GET /api/v1/auth/me
2025-06-29 01:13:58 [http-nio-8888-exec-9] DEBUG o.s.security.web.FilterChainProxy - Securing GET /api/v1/auth/me
2025-06-29 01:13:58 [http-nio-8888-exec-9] DEBUG o.s.security.web.FilterChainProxy - Secured GET /api/v1/auth/me
2025-06-29 01:14:22 [http-nio-8888-exec-10] DEBUG o.s.security.web.FilterChainProxy - Securing GET /api/v1/auth/me
2025-06-29 01:14:22 [http-nio-8888-exec-10] DEBUG o.s.security.web.FilterChainProxy - Secured GET /api/v1/auth/me
2025-06-29 01:14:45 [http-nio-8888-exec-6] DEBUG o.s.security.web.FilterChainProxy - Securing GET /api/v1/auth/me
2025-06-29 01:14:45 [http-nio-8888-exec-6] DEBUG o.s.security.web.FilterChainProxy - Secured GET /api/v1/auth/me
2025-06-29 01:14:57 [http-nio-8888-exec-7] DEBUG o.s.security.web.FilterChainProxy - Securing GET /api/v1/auth/me
2025-06-29 01:14:57 [http-nio-8888-exec-7] DEBUG o.s.security.web.FilterChainProxy - Secured GET /api/v1/auth/me
2025-06-29 01:15:16 [http-nio-8888-exec-8] DEBUG o.s.security.web.FilterChainProxy - Securing GET /api/v1/auth/me
2025-06-29 01:15:16 [http-nio-8888-exec-8] DEBUG o.s.security.web.FilterChainProxy - Secured GET /api/v1/auth/me
2025-06-29 01:15:20 [http-nio-8888-exec-1] DEBUG o.s.security.web.FilterChainProxy - Securing GET /api/v1/auth/me
2025-06-29 01:15:20 [http-nio-8888-exec-1] DEBUG o.s.security.web.FilterChainProxy - Secured GET /api/v1/auth/me
2025-06-29 01:15:22 [http-nio-8888-exec-2] DEBUG o.s.security.web.FilterChainProxy - Securing GET /api/v1/auth/me
2025-06-29 01:15:22 [http-nio-8888-exec-2] DEBUG o.s.security.web.FilterChainProxy - Secured GET /api/v1/auth/me
2025-06-29 01:15:23 [http-nio-8888-exec-3] DEBUG o.s.security.web.FilterChainProxy - Securing GET /api/v1/auth/me
2025-06-29 01:15:23 [http-nio-8888-exec-3] DEBUG o.s.security.web.FilterChainProxy - Secured GET /api/v1/auth/me
2025-06-29 01:15:31 [http-nio-8888-exec-4] DEBUG o.s.security.web.FilterChainProxy - Securing GET /api/v1/auth/me
2025-06-29 01:15:31 [http-nio-8888-exec-4] DEBUG o.s.security.web.FilterChainProxy - Secured GET /api/v1/auth/me
2025-06-29 01:15:37 [http-nio-8888-exec-5] DEBUG o.s.security.web.FilterChainProxy - Securing GET /api/v1/auth/me
2025-06-29 01:15:37 [http-nio-8888-exec-5] DEBUG o.s.security.web.FilterChainProxy - Secured GET /api/v1/auth/me
2025-06-29 01:15:42 [http-nio-8888-exec-9] DEBUG o.s.security.web.FilterChainProxy - Securing GET /api/v1/auth/me
2025-06-29 01:15:42 [http-nio-8888-exec-9] DEBUG o.s.security.web.FilterChainProxy - Secured GET /api/v1/auth/me
2025-06-29 01:15:56 [http-nio-8888-exec-10] DEBUG o.s.security.web.FilterChainProxy - Securing GET /api/v1/auth/me
2025-06-29 01:15:57 [http-nio-8888-exec-10] DEBUG o.s.security.web.FilterChainProxy - Secured GET /api/v1/auth/me
2025-06-29 01:16:05 [http-nio-8888-exec-6] DEBUG o.s.security.web.FilterChainProxy - Securing GET /api/v1/auth/me
2025-06-29 01:16:05 [http-nio-8888-exec-6] DEBUG o.s.security.web.FilterChainProxy - Secured GET /api/v1/auth/me
2025-06-29 01:16:08 [http-nio-8888-exec-7] DEBUG o.s.security.web.FilterChainProxy - Securing GET /api/v1/auth/me
2025-06-29 01:16:08 [http-nio-8888-exec-7] DEBUG o.s.security.web.FilterChainProxy - Secured GET /api/v1/auth/me
2025-06-29 01:17:15 [http-nio-8888-exec-1] DEBUG o.s.security.web.FilterChainProxy - Securing GET /api/v1/auth/me
2025-06-29 01:17:15 [http-nio-8888-exec-1] DEBUG o.s.security.web.FilterChainProxy - Secured GET /api/v1/auth/me
