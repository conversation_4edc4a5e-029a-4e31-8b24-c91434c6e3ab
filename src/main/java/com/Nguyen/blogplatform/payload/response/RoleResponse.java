package com.Nguyen.blogplatform.payload.response;

import com.Nguyen.blogplatform.Enum.ERole;
import lombok.*;

import java.util.List;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class RoleResponse {
    private Integer id;
    private ERole name;
    private String displayName;
    private List<UserResponse> users;
    private Long userCount;
    
    public RoleResponse(Integer id, ERole name, Long userCount) {
        this.id = id;
        this.name = name;
        this.displayName = name.name();
        this.userCount = userCount;
    }
}
